"use client"

import Link from "next/link"
import { ExternalLink } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"

interface TermsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function TermsModal({ open, onOpenChange }: TermsModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Terms of Service</DialogTitle>
          <DialogDescription>Effective Date: May 14, 2025</DialogDescription>
        </DialogHeader>

        <div className="prose prose-sm prose-neutral dark:prose-invert max-w-none py-4">
          <p>
            These Terms of Service ("Terms") govern your access to and use of the Togeda.ai platform
            (the "Service"), operated by Valencia Drive Commerce LLC ("we," "our," or "us"). By
            using the Service, you agree to these Terms. If you do not agree, please do not use our
            platform.
          </p>

          <h3>1. Eligibility & Account Management</h3>
          <ul>
            <li>You must be at least 13 years old to use Togeda.ai.</li>
            <li>
              Each user must create and maintain their own account; account sharing is prohibited.
            </li>
            <li>
              You are responsible for maintaining the confidentiality of your account credentials.
            </li>
          </ul>

          <h3>2. Permitted Uses</h3>
          <ul>
            <li>
              You may use the Service for personal and commercial purposes (e.g., organizing group
              trips, retreats, and events).
            </li>
            <li>All use must comply with applicable laws and these Terms.</li>
          </ul>

          <h3>3. User-Generated Content</h3>
          <ul>
            <li>
              You retain ownership of content you create, including trip names, itineraries, and
              messages.
            </li>
            <li>
              We retain rights to use anonymized and aggregated data for analytics and service
              improvement.
            </li>
            <li>
              Content is not displayed publicly unless you choose to share it through the platform.
            </li>
          </ul>

          <h3>4. Intellectual Property</h3>
          <ul>
            <li>
              All content generated by the Service, including AI-generated itineraries and
              recommendations, is the intellectual property of Valencia Drive Commerce LLC.
            </li>
            <li>
              You are permitted to share and export content for personal and commercial use but may
              not resell or distribute it as a commercial product.
            </li>
          </ul>

          <h3>5. Payments & Refund Policy</h3>
          <ul>
            <li>Payments are securely processed through Stripe.</li>
            <li>All purchases are non-refundable unless otherwise required by law.</li>
            <li>For payment disputes, please contact <NAME_EMAIL>.</li>
          </ul>

          <h3>6. Termination & Suspension of Accounts</h3>
          <ul>
            <li>
              We reserve the right to issue warnings, temporarily suspend, or permanently terminate
              accounts that violate these Terms, based on the severity and frequency of violations.
            </li>
            <li>
              Examples of violations include misuse of payment systems, abusive behavior, fraudulent
              activity, or attempts to bypass app functionality.
            </li>
          </ul>

          <h3>7. Disclaimers & Limitation of Liability</h3>
          <ul>
            <li>
              Togeda.ai is a planning and coordination tool; we are not responsible for trip
              cancellations, third-party services, or the safety of travel activities.
            </li>
            <li>
              Our Service is provided "as-is" and "as-available" without warranties of any kind.
            </li>
            <li>
              To the fullest extent permitted by law, we disclaim all liability for damages related
              to your use of the Service.
            </li>
          </ul>

          <h3>8. Privacy</h3>
          <ul>
            <li>
              Your use of the Service is also governed by our Privacy Policy, which outlines how we
              collect, use, and protect your personal information.
            </li>
          </ul>

          <h3>9. Governing Law & Dispute Resolution</h3>
          <ul>
            <li>
              These Terms are governed by the laws of the State of Texas, without regard to conflict
              of law principles.
            </li>
            <li>Any disputes will be resolved in the courts located in McLennan County, Texas.</li>
          </ul>

          <h3>10. Modifications to Terms</h3>
          <ul>
            <li>
              We may update these Terms from time to time. Continued use of the Service after
              changes are posted constitutes your acceptance of the updated Terms.
            </li>
          </ul>

          <h3>11. Contact Us</h3>
          <p>
            For questions about these Terms, contact us at:
            <br />
            Valencia Drive Commerce LLC
            <br />
            Email: <EMAIL>
          </p>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <DialogClose asChild>
            <Button variant="outline">Close</Button>
          </DialogClose>
          <Link
            href="https://vdc.formaloo.me/brotrips-ai-contact-us"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button className="flex items-center gap-2">
              Contact Us <ExternalLink className="h-4 w-4" />
            </Button>
          </Link>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
